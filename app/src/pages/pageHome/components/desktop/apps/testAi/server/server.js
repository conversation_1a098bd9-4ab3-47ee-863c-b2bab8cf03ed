import http from "node:http";
import url from "node:url";

const PORT = 5004;

const server = http.createServer((req, res) => {
	// Enable CORS
	res.setHeader("Access-Control-Allow-Origin", "*");
	res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
	res.setHeader("Access-Control-Allow-Headers", "Content-Type");

	// Handle preflight OPTIONS request
	if (req.method === "OPTIONS") {
		res.writeHead(200);
		res.end();
		return;
	}

	const parsedUrl = url.parse(req.url, true);

	if (req.method === "POST" && parsedUrl.pathname === "/message") {
		let body = "";

		req.on("data", (chunk) => {
			body += chunk.toString();
		});

		req.on("end", () => {
			try {
				// Parse the message from the request body
				const data = JSON.parse(body);
				const message = data.message;

				console.log(`Received message: ${message}`);

				// Always respond with "hello"
				res.writeHead(200, { "Content-Type": "application/json" });
				res.end(JSON.stringify({ response: "hello" }));
			} catch (error) {
				console.error("Error parsing request:", error);
				res.writeHead(400, { "Content-Type": "application/json" });
				res.end(JSON.stringify({ error: "Invalid JSON" }));
			}
		});
	} else {
		// Handle other routes
		res.writeHead(404, { "Content-Type": "application/json" });
		res.end(JSON.stringify({ error: "Not found" }));
	}
});

server.listen(PORT, () => {
	console.log(`Server running on port ${PORT}`);
	console.log(`Accepting POST requests at http://localhost:${PORT}/message`);
});

// Handle graceful shutdown
process.on("SIGINT", () => {
	console.log("\nShutting down server...");
	server.close(() => {
		console.log("Server closed");
		process.exit(0);
	});
});
